# 通用兑换码系统设计文档

## 概述

本系统实现了一个通用的兑换码管理功能，支持多种兑换类型（积分、会员、VIP等），管理员可以创建和管理兑换码，用户可以使用兑换码获得相应奖励。

## 系统特性

### 1. 多类型支持
- **积分兑换码**: 用户兑换后获得指定积分
- **会员兑换码**: 用户兑换后获得会员权益（待实现）
- **VIP兑换码**: 用户兑换后获得VIP等级和天数（待实现）
- **自定义兑换码**: 支持扩展的自定义奖励类型（待实现）

### 2. 灵活的使用控制
- **使用次数限制**: 支持单次使用、多次使用或无限制使用
- **有效期控制**: 支持设置过期时间或永不过期
- **激活状态**: 支持启用/禁用兑换码

### 3. 批量管理
- **批量生成**: 支持一次性生成多个相同配置的兑换码
- **批次管理**: 通过批次ID管理批量生成的兑换码
- **批量删除**: 支持按批次删除兑换码

### 4. 完整的记录追踪
- **使用记录**: 记录每次兑换的详细信息
- **IP和用户代理**: 记录使用者的网络信息
- **统计分析**: 提供多维度的统计数据

## 数据库设计

### 主要表结构

#### 1. sys_redeem_codes (兑换码表)
```sql
CREATE TABLE `sys_redeem_codes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `code` varchar(191) NOT NULL COMMENT '兑换码',
  `type` varchar(191) NOT NULL COMMENT '兑换类型',
  `title` varchar(191) NOT NULL COMMENT '兑换码标题',
  `description` longtext COMMENT '描述',
  `reward_data` text COMMENT '奖励数据JSON',
  `max_uses` int DEFAULT '1' COMMENT '最大使用次数',
  `used_count` int DEFAULT '0' COMMENT '已使用次数',
  `expires_at` datetime(3) DEFAULT NULL COMMENT '过期时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者ID',
  `batch_id` varchar(191) DEFAULT NULL COMMENT '批次ID',
  `remark` longtext COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_sys_redeem_codes_deleted_at` (`deleted_at`),
  KEY `idx_sys_redeem_codes_type` (`type`),
  KEY `idx_sys_redeem_codes_batch_id` (`batch_id`)
);
```

#### 2. sys_redeem_code_usage (兑换码使用记录表)
```sql
CREATE TABLE `sys_redeem_code_usage` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `code_id` bigint unsigned NOT NULL COMMENT '兑换码ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `reward_data` text COMMENT '获得奖励数据',
  `used_at` datetime(3) NOT NULL COMMENT '使用时间',
  `ip_address` longtext COMMENT '使用者IP',
  `user_agent` longtext COMMENT '用户代理',
  PRIMARY KEY (`id`),
  KEY `idx_sys_redeem_code_usage_deleted_at` (`deleted_at`),
  KEY `idx_sys_redeem_code_usage_code_id` (`code_id`),
  KEY `idx_sys_redeem_code_usage_user_id` (`user_id`)
);
```

## API接口设计

### 管理员接口

#### 1. 创建兑换码
- **接口**: `POST /redeemCode/create`
- **权限**: 管理员 (888, 8881)
- **功能**: 创建单个兑换码

#### 2. 批量创建兑换码
- **接口**: `POST /redeemCode/batchCreate`
- **权限**: 管理员 (888, 8881)
- **功能**: 批量创建兑换码

#### 3. 获取兑换码列表
- **接口**: `GET /redeemCode/list`
- **权限**: 管理员 (888, 8881)
- **功能**: 分页查询兑换码列表，支持多种筛选条件

#### 4. 更新兑换码
- **接口**: `PUT /redeemCode/update`
- **权限**: 管理员 (888, 8881)
- **功能**: 更新兑换码信息

#### 5. 删除兑换码
- **接口**: `DELETE /redeemCode/{id}`
- **权限**: 管理员 (888, 8881)
- **功能**: 删除单个兑换码

#### 6. 批量删除兑换码
- **接口**: `DELETE /redeemCode/batch/{batchId}`
- **权限**: 管理员 (888, 8881)
- **功能**: 按批次删除兑换码

#### 7. 获取使用记录
- **接口**: `GET /redeemCode/usage/list`
- **权限**: 管理员 (888, 8881)
- **功能**: 查看兑换码使用记录

#### 8. 获取统计信息
- **接口**: `GET /redeemCode/stats`
- **权限**: 管理员 (888, 8881)
- **功能**: 获取兑换码统计数据

### 用户接口

#### 1. 使用兑换码
- **接口**: `POST /redeemCode/use`
- **权限**: 普通用户 (666)
- **功能**: 使用兑换码获得奖励

#### 2. 验证兑换码
- **接口**: `GET /redeemCode/validate`
- **权限**: 普通用户 (666) 
- **功能**: 验证兑换码是否有效（不实际使用）

#### 3. 获取兑换历史
- **接口**: `GET /redeemCode/user/history`
- **权限**: 普通用户 (666)
- **功能**: 查看个人兑换历史

## 业务流程

### 1. 管理员创建兑换码流程
1. 管理员选择兑换类型（积分/会员/VIP/自定义）
2. 设置奖励数据（如积分数量、会员套餐等）
3. 配置使用限制（使用次数、有效期等）
4. 系统生成唯一兑换码
5. 保存到数据库

### 2. 用户使用兑换码流程
1. 用户输入兑换码
2. 系统验证兑换码有效性
   - 检查是否存在
   - 检查是否激活
   - 检查是否过期
   - 检查使用次数是否达到上限
   - 检查用户是否已使用（单次使用限制）
3. 根据兑换类型处理奖励
   - 积分类型：调用积分服务添加积分
   - 其他类型：调用相应服务处理
4. 更新兑换码使用次数
5. 记录使用记录
6. 返回兑换结果

## 权限控制

### 角色权限分配

#### 超级管理员 (888)
- 所有兑换码管理权限
- 所有统计查看权限

#### 管理员 (8881)
- 所有兑换码管理权限
- 所有统计查看权限

#### 普通用户 (666)
- 使用兑换码权限
- 验证兑换码权限
- 查看个人兑换历史权限

## 扩展性设计

### 1. 新增兑换类型
要添加新的兑换类型，需要：
1. 在 `RedeemCodeType` 常量中添加新类型
2. 在 `RewardData` 结构体中添加相应字段
3. 在 `processReward` 方法中添加处理逻辑
4. 在 `getRewardMessage` 方法中添加消息模板

### 2. 自定义验证规则
可以通过扩展 `CanUse` 方法来添加更复杂的验证逻辑，如：
- 用户等级限制
- 地域限制
- 时间段限制

### 3. 统计维度扩展
可以在 `GetRedeemCodeStats` 方法中添加更多统计维度，如：
- 按地域统计
- 按用户等级统计
- 按时间段统计

## 安全考虑

### 1. 兑换码生成
- 使用加密安全的随机数生成器
- 12位长度，包含大写字母和数字
- 确保唯一性检查

### 2. 防刷机制
- 记录IP地址和用户代理
- 可扩展添加频率限制
- 可扩展添加验证码验证

### 3. 权限控制
- 严格的角色权限分离
- 管理员操作记录日志
- 敏感操作需要额外验证

## 监控和日志

### 1. 操作日志
- 所有管理员操作都会记录到操作日志
- 包含操作者、操作时间、操作内容

### 2. 使用记录
- 详细记录每次兑换的信息
- 支持审计和分析

### 3. 统计监控
- 实时统计数据
- 支持多维度分析
- 可用于业务决策

## 部署说明

### 1. 数据库迁移
系统会自动创建相关数据表，无需手动执行SQL。

### 2. 权限初始化
权限配置已添加到 `casbin.go` 中，系统初始化时会自动配置。

### 3. 任务配置
已在 `task_config.go` 中添加兑换码任务ID，用于积分流水记录。

## 使用示例

### 创建积分兑换码
```json
{
  "type": "points",
  "title": "新用户奖励",
  "description": "新用户注册奖励100积分",
  "reward_data": {
    "points": 100
  },
  "max_uses": 1,
  "expires_at": "2024-12-31T23:59:59Z",
  "remark": "新用户活动"
}
```

### 使用兑换码
```json
{
  "code": "ABC123DEF456"
}
```

### 响应示例
```json
{
  "code": 0,
  "data": {
    "type": "points",
    "title": "新用户奖励",
    "reward_data": {
      "points": 100
    },
    "code": "ABC123DEF456",
    "description": "新用户注册奖励100积分",
    "message": "成功兑换100积分"
  },
  "msg": "兑换成功"
}
```
