package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
)

func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}
func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]
	holder(publicGroup, privateGroup)
	{
		mcpRouter := router.RouterGroupApp.Mcp
		mcpRouter.InitProjectsRouter(privateGroup, publicGroup)
		mcpRouter.InitProjectToolsRouter(publicGroup)
		mcpRouter.InitMcpClientRouter(privateGroup, publicGroup)
		mcpRouter.InitApiKeyRouter(privateGroup)
		mcpRouter.InitUseCaseRouter(privateGroup, publicGroup)
		mcpRouter.InitToolsRouter(privateGroup, publicGroup)
		mcpRouter.InitMapCoordinatesRouter(privateGroup, publicGroup)
		mcpRouter.InitBindingRouter(publicGroup) // 绑定功能路由
		mcpRouter.InitSunoTaskRouter(privateGroup, publicGroup) // Suno任务路由
	}
	{
		authingRouter := router.RouterGroupApp.Authing
		authingRouter.InitAuthRouter(privateGroup, publicGroup)
		authingRouter.InitAuthingV3Router(privateGroup, publicGroup)
	}
	{
		translateRouter := router.RouterGroupApp.Translate
		translateRouter.InitTranslateRouter(privateGroup, publicGroup)
	}
	{
		llmRouter := router.RouterGroupApp.LLM
		llmRouter.InitLLMRouter(privateGroup, publicGroup)
	}
	{
		paymentRouter := router.RouterGroupApp.Payment
		paymentRouter.InitPaymentRouter(privateGroup, publicGroup)
	}
	{
		productRouter := router.RouterGroupApp.Product
		productRouter.InitProductRouter(privateGroup, publicGroup)
		productRouter.InitMembershipRouter(privateGroup)
		productRouter.InitUserAddressRouter(privateGroup)
	}
	{
		clipboardRouter := router.RouterGroupApp.Clipboard
		clipboardRouter.InitClipboardRouter(privateGroup)
	}
	{
		wechatWorkRouter := router.RouterGroupApp.WechatWork
		wechatWorkRouter.InitWechatWorkRouter(privateGroup, publicGroup)
	}
	{
		webSocketRouter := router.RouterGroupApp.WebSocket
		webSocketRouter.InitWebSocketRouter(privateGroup)
	}
	{
		aliyunRouter := router.RouterGroupApp.Aliyun
		aliyunRouter.InitAliyunRouter(privateGroup, publicGroup)
	}
	{
		deviceRouter := router.RouterGroupApp.Device
		deviceRouter.InitDeviceRouter(privateGroup, publicGroup)
		deviceRouter.InitDeviceVersionRecordRouter(privateGroup, publicGroup)
	}
	{
		systemRouter := router.RouterGroupApp.System
		systemRouter.InitSysVersionRouter(privateGroup, publicGroup)
	}
	{
		integralRouter := router.RouterGroupApp.Integral
		integralRouter.InitSysTaskRouter(privateGroup)
		integralRouter.InitSysUserPointsRouter(privateGroup)
		integralRouter.InitFeedbackRouter(privateGroup, publicGroup)
		integralRouter.InitRedeemCodeRouter(privateGroup)
	}
}
