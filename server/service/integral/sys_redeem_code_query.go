package integral

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	integralRes "github.com/flipped-aurora/gin-vue-admin/server/model/integral/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
)

// GetRedeemCodeUsageList 获取兑换码使用记录列表
func (s *RedeemCodeService) GetRedeemCodeUsageList(req integralReq.RedeemCodeUsageSearch) (integralRes.RedeemCodeUsageListResponse, error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	db := global.GVA_DB.Model(&integral.SysRedeemCodeUsage{}).
		Joins("LEFT JOIN sys_redeem_codes ON sys_redeem_code_usage.code_id = sys_redeem_codes.id").
		Joins("LEFT JOIN sys_users ON sys_redeem_code_usage.user_id = sys_users.id")

	// 添加查询条件
	if req.CodeID != 0 {
		db = db.Where("sys_redeem_code_usage.code_id = ?", req.CodeID)
	}
	if req.UserID != 0 {
		db = db.Where("sys_redeem_code_usage.user_id = ?", req.UserID)
	}
	if req.Type != "" {
		db = db.Where("sys_redeem_codes.type = ?", req.Type)
	}
	if req.StartDate != "" {
		db = db.Where("sys_redeem_code_usage.used_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		db = db.Where("sys_redeem_code_usage.used_at <= ?", req.EndDate+" 23:59:59")
	}

	// 获取总数
	var total int64
	err := db.Count(&total).Error
	if err != nil {
		return integralRes.RedeemCodeUsageListResponse{}, err
	}

	// 获取列表
	var usages []struct {
		integral.SysRedeemCodeUsage
		Code     string `json:"code"`
		Type     string `json:"type"`
		Title    string `json:"title"`
		Username string `json:"username"`
	}

	err = db.Select("sys_redeem_code_usage.*, sys_redeem_codes.code, sys_redeem_codes.type, sys_redeem_codes.title, sys_users.username").
		Order("sys_redeem_code_usage.used_at DESC").
		Limit(limit).Offset(offset).
		Find(&usages).Error
	if err != nil {
		return integralRes.RedeemCodeUsageListResponse{}, err
	}

	// 转换为响应格式
	var list []integralRes.RedeemCodeUsageResponse
	for _, usage := range usages {
		var rewardData integral.RewardData
		if usage.RewardData != "" {
			json.Unmarshal([]byte(usage.RewardData), &rewardData)
		}

		list = append(list, integralRes.RedeemCodeUsageResponse{
			ID:         usage.ID,
			Code:       usage.Code,
			Type:       usage.Type,
			Title:      usage.Title,
			UserID:     usage.UserID,
			Username:   usage.Username,
			RewardData: rewardData,
			UsedAt:     global.DateTime(usage.UsedAt),
			IPAddress:  usage.IPAddress,
		})
	}

	return integralRes.RedeemCodeUsageListResponse{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetRedeemCodeStats 获取兑换码统计信息
func (s *RedeemCodeService) GetRedeemCodeStats() (integralRes.RedeemCodeStatsResponse, error) {
	var stats integralRes.RedeemCodeStatsResponse

	// 总兑换码数
	global.GVA_DB.Model(&integral.SysRedeemCode{}).Count(&stats.TotalCodes)

	// 激活的兑换码数
	global.GVA_DB.Model(&integral.SysRedeemCode{}).
		Where("is_active = ? AND (expires_at IS NULL OR expires_at > ?) AND (max_uses = 0 OR used_count < max_uses)", 
			true, time.Now()).Count(&stats.ActiveCodes)

	// 已使用的兑换码数
	global.GVA_DB.Model(&integral.SysRedeemCode{}).
		Where("used_count > 0").Count(&stats.UsedCodes)

	// 已过期的兑换码数
	global.GVA_DB.Model(&integral.SysRedeemCode{}).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).Count(&stats.ExpiredCodes)

	// 使用次数统计
	global.GVA_DB.Model(&integral.SysRedeemCodeUsage{}).Count(&stats.TotalUsages)

	// 今日使用次数
	today := time.Now().Format("2006-01-02")
	global.GVA_DB.Model(&integral.SysRedeemCodeUsage{}).
		Where("DATE(used_at) = ?", today).Count(&stats.TodayUsages)

	// 本周使用次数
	weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday())).Format("2006-01-02")
	global.GVA_DB.Model(&integral.SysRedeemCodeUsage{}).
		Where("used_at >= ?", weekStart).Count(&stats.WeekUsages)

	// 本月使用次数
	monthStart := time.Now().AddDate(0, 0, -time.Now().Day()+1).Format("2006-01-02")
	global.GVA_DB.Model(&integral.SysRedeemCodeUsage{}).
		Where("used_at >= ?", monthStart).Count(&stats.MonthUsages)

	// 按类型统计
	var typeStats []struct {
		Type        string `json:"type"`
		TotalCodes  int64  `json:"total_codes"`
		UsedCodes   int64  `json:"used_codes"`
		TotalUsages int64  `json:"total_usages"`
	}

	// 获取所有类型的统计
	global.GVA_DB.Model(&integral.SysRedeemCode{}).
		Select("type, COUNT(*) as total_codes, COUNT(CASE WHEN used_count > 0 THEN 1 END) as used_codes").
		Group("type").
		Find(&typeStats)

	// 获取每种类型的使用次数
	for i := range typeStats {
		global.GVA_DB.Model(&integral.SysRedeemCodeUsage{}).
			Joins("LEFT JOIN sys_redeem_codes ON sys_redeem_code_usage.code_id = sys_redeem_codes.id").
			Where("sys_redeem_codes.type = ?", typeStats[i].Type).
			Count(&typeStats[i].TotalUsages)
	}

	// 转换为map格式
	stats.TypeStats = make(map[string]integralRes.TypeStatItem)
	for _, stat := range typeStats {
		stats.TypeStats[stat.Type] = integralRes.TypeStatItem{
			Type:        stat.Type,
			TotalCodes:  stat.TotalCodes,
			UsedCodes:   stat.UsedCodes,
			TotalUsages: stat.TotalUsages,
		}
	}

	return stats, nil
}

// GetUserRedeemHistory 获取用户兑换历史
func (s *RedeemCodeService) GetUserRedeemHistory(userID uint, req integralReq.RedeemCodeUsageSearch) (integralRes.RedeemCodeUsageListResponse, error) {
	req.UserID = userID
	return s.GetRedeemCodeUsageList(req)
}

// ValidateRedeemCode 验证兑换码（不实际使用）
func (s *RedeemCodeService) ValidateRedeemCode(code string) (*integral.SysRedeemCode, error) {
	var redeemCode integral.SysRedeemCode
	if err := global.GVA_DB.Where("code = ?", strings.ToUpper(code)).First(&redeemCode).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("兑换码不存在")
		}
		return nil, fmt.Errorf("查询兑换码失败: %v", err)
	}

	if !redeemCode.CanUse() {
		if !redeemCode.IsActive {
			return nil, fmt.Errorf("兑换码已被禁用")
		}
		if redeemCode.IsExpired() {
			return nil, fmt.Errorf("兑换码已过期")
		}
		if redeemCode.IsExhausted() {
			return nil, fmt.Errorf("兑换码使用次数已达上限")
		}
	}

	return &redeemCode, nil
}

// GetRedeemCodeByID 根据ID获取兑换码详情
func (s *RedeemCodeService) GetRedeemCodeByID(id uint) (integralRes.RedeemCodeResponse, error) {
	var redeemCode integral.SysRedeemCode
	if err := global.GVA_DB.First(&redeemCode, id).Error; err != nil {
		return integralRes.RedeemCodeResponse{}, err
	}

	// 获取创建者信息
	var user system.SysUser
	createdByName := "未知"
	if err := global.GVA_DB.First(&user, redeemCode.CreatedBy).Error; err == nil {
		createdByName = user.NickName
	}

	return integralRes.ConvertToRedeemCodeResponse(redeemCode, createdByName), nil
}
