package integral

import (
	"encoding/json"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// SysRedeemCode 通用兑换码表
type SysRedeemCode struct {
	global.GVA_MODEL
	Code        string     `json:"code" gorm:"uniqueIndex;not null;comment:兑换码"`                    // 兑换码（唯一）
	Type        string     `json:"type" gorm:"not null;index;comment:兑换类型"`                         // 兑换类型：points/membership/vip等
	Title       string     `json:"title" gorm:"not null;comment:兑换码标题"`                            // 兑换码标题
	Description string     `json:"description" gorm:"comment:描述"`                                   // 描述
	RewardData  string     `json:"reward_data" gorm:"type:text;comment:奖励数据JSON"`                  // 奖励数据（JSON格式）
	MaxUses     int        `json:"max_uses" gorm:"default:1;comment:最大使用次数"`                        // 最大使用次数，0表示无限制
	UsedCount   int        `json:"used_count" gorm:"default:0;comment:已使用次数"`                       // 已使用次数
	ExpiresAt   *time.Time `json:"expires_at" gorm:"comment:过期时间"`                                  // 过期时间，null表示永不过期
	IsActive    bool       `json:"is_active" gorm:"default:true;comment:是否激活"`                      // 是否激活
	CreatedBy   uint       `json:"created_by" gorm:"comment:创建者ID"`                                 // 创建者ID（管理员）
	BatchID     string     `json:"batch_id" gorm:"index;comment:批次ID"`                             // 批次ID，用于批量生成
	Remark      string     `json:"remark" gorm:"comment:备注"`                                        // 备注
}

func (SysRedeemCode) TableName() string {
	return "sys_redeem_codes"
}

// SysRedeemCodeUsage 兑换码使用记录表
type SysRedeemCodeUsage struct {
	global.GVA_MODEL
	CodeID     uint      `json:"code_id" gorm:"index;not null;comment:兑换码ID"`     // 兑换码ID
	UserID     uint      `json:"user_id" gorm:"index;not null;comment:用户ID"`      // 使用者ID
	RewardData string    `json:"reward_data" gorm:"type:text;comment:获得奖励数据"`    // 获得奖励数据（JSON格式）
	UsedAt     time.Time `json:"used_at" gorm:"not null;comment:使用时间"`            // 使用时间
	IPAddress  string    `json:"ip_address" gorm:"comment:使用者IP"`                 // 使用者IP
	UserAgent  string    `json:"user_agent" gorm:"comment:用户代理"`                 // 用户代理
}

func (SysRedeemCodeUsage) TableName() string {
	return "sys_redeem_code_usage"
}

// 兑换码类型常量
const (
	RedeemCodeTypePoints     = "points"     // 积分兑换码
	RedeemCodeTypeMembership = "membership" // 会员兑换码
	RedeemCodeTypeVIP        = "vip"        // VIP兑换码
	RedeemCodeTypeCustom     = "custom"     // 自定义兑换码
)

// 兑换码状态常量
const (
	RedeemCodeStatusActive    = "active"    // 激活状态
	RedeemCodeStatusInactive  = "inactive"  // 未激活状态
	RedeemCodeStatusExpired   = "expired"   // 已过期
	RedeemCodeStatusExhausted = "exhausted" // 已用完
)

// RewardData 奖励数据结构
type RewardData struct {
	Points       int    `json:"points,omitempty"`        // 积分数量
	MembershipID uint   `json:"membership_id,omitempty"` // 会员套餐ID
	VIPLevel     int    `json:"vip_level,omitempty"`     // VIP等级
	VIPDays      int    `json:"vip_days,omitempty"`      // VIP天数
	CustomData   string `json:"custom_data,omitempty"`   // 自定义数据
}

// GetRewardData 获取奖励数据
func (r *SysRedeemCode) GetRewardData() (*RewardData, error) {
	var reward RewardData
	if r.RewardData == "" {
		return &reward, nil
	}
	err := json.Unmarshal([]byte(r.RewardData), &reward)
	return &reward, err
}

// SetRewardData 设置奖励数据
func (r *SysRedeemCode) SetRewardData(reward RewardData) error {
	data, err := json.Marshal(reward)
	if err != nil {
		return err
	}
	r.RewardData = string(data)
	return nil
}

// IsExpired 检查兑换码是否过期
func (r *SysRedeemCode) IsExpired() bool {
	if r.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*r.ExpiresAt)
}

// IsExhausted 检查兑换码是否已用完
func (r *SysRedeemCode) IsExhausted() bool {
	if r.MaxUses == 0 {
		return false // 无限制使用
	}
	return r.UsedCount >= r.MaxUses
}

// CanUse 检查兑换码是否可以使用
func (r *SysRedeemCode) CanUse() bool {
	return r.IsActive && !r.IsExpired() && !r.IsExhausted()
}

// GetStatus 获取兑换码状态
func (r *SysRedeemCode) GetStatus() string {
	if !r.IsActive {
		return RedeemCodeStatusInactive
	}
	if r.IsExpired() {
		return RedeemCodeStatusExpired
	}
	if r.IsExhausted() {
		return RedeemCodeStatusExhausted
	}
	return RedeemCodeStatusActive
}
