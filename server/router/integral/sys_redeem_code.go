package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type RedeemCodeRouter struct{}

// InitRedeemCodeRouter 初始化兑换码路由信息
func (r *RedeemCodeRouter) InitRedeemCodeRouter(Router *gin.RouterGroup) {
	redeemCodeRouter := Router.Group("redeemCode").Use(middleware.OperationRecord())
	redeemCodeRouterWithoutRecord := Router.Group("redeemCode")
	var redeemCodeApi = v1.ApiGroupApp.IntegralApiGroup.RedeemCodeApi
	
	// 需要记录操作日志的路由
	{
		redeemCodeRouter.POST("create", redeemCodeApi.CreateRedeemCode)                     // 创建兑换码
		redeemCodeRouter.POST("batchCreate", redeemCodeApi.BatchCreateRedeemCode)          // 批量创建兑换码
		redeemCodeRouter.PUT("update", redeemCodeApi.UpdateRedeemCode)                     // 更新兑换码
		redeemCodeRouter.DELETE(":id", redeemCodeApi.DeleteRedeemCode)                     // 删除兑换码
		redeemCodeRouter.DELETE("batch/:batchId", redeemCodeApi.DeleteRedeemCodeBatch)     // 批量删除兑换码
		redeemCodeRouter.POST("use", redeemCodeApi.UseRedeemCode)                          // 使用兑换码
	}
	
	// 不需要记录操作日志的路由
	{
		redeemCodeRouterWithoutRecord.GET("list", redeemCodeApi.GetRedeemCodeList)         // 获取兑换码列表
		redeemCodeRouterWithoutRecord.GET(":id", redeemCodeApi.GetRedeemCodeByID)          // 根据ID获取兑换码详情
		redeemCodeRouterWithoutRecord.GET("validate", redeemCodeApi.ValidateRedeemCode)    // 验证兑换码
		redeemCodeRouterWithoutRecord.GET("usage/list", redeemCodeApi.GetRedeemCodeUsageList) // 获取兑换码使用记录列表
		redeemCodeRouterWithoutRecord.GET("user/history", redeemCodeApi.GetUserRedeemHistory) // 获取用户兑换历史
		redeemCodeRouterWithoutRecord.GET("stats", redeemCodeApi.GetRedeemCodeStats)       // 获取兑换码统计信息
	}
}
